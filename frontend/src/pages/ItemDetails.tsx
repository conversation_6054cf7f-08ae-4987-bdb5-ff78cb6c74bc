import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Dialog,
  DialogContent,
  IconButton,
} from '@mui/material';
import { Close as CloseIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import api, { imageApi } from '../api/axios';
import { Helmet, HelmetProvider } from 'react-helmet-async';

interface Item {
  id: number;
  name: string;
  description: string;
  article_number: string;
  price: number;
  quantity: number;
  is_used: boolean;
  brand: {
    id: number;
    name: string;
  };
  category: {
    id: number;
    name: string;
  };
  images: {
    id: number;
    image_path: string;
  }[];
}

const ItemDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [item, setItem] = useState<Item | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  useEffect(() => {
    const fetchItem = async () => {
      try {
        const response = await api.get(`/items/${id}?active_only=true`);
        setItem(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load item details');
        setLoading(false);
      }
    };

    fetchItem();
  }, [id]);

  const handleImageClick = (imagePath: string) => {
    setSelectedImage(imagePath);
  };

  const handleCloseModal = () => {
    setSelectedImage(null);
  };

  if (loading) {
    return (
      <Container>
        <Typography>Loading...</Typography>
      </Container>
    );
  }

  if (error || !item) {
    return (
      <Container>
        <Typography color="error">{error || 'Item not found'}</Typography>
        <Button onClick={() => navigate('/items')}>Back to Items</Button>
      </Container>
    );
  }

  return (
    <HelmetProvider>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Helmet>
          <title>{item?.name || 'Item Details'} - LSK Trading</title>
          <meta name="description" content={item?.description || 'View item details'} />
          {item && (
            <script type="application/ld+json">
              {JSON.stringify({
                '@context': 'https://schema.org',
                '@type': 'Product',
                name: item.name,
                description: item.description,
                image: item.images?.[0]?.image_path ? `https://lsktrading.com${item.images[0].image_path}` : undefined,
                brand: {
                  '@type': 'Brand',
                  name: item.brand?.name || 'LSK Trading'
                },
                offers: {
                  '@type': 'Offer',
                  price: item.price,
                  priceCurrency: 'USD',
                  availability: 'https://schema.org/InStock',
                  itemCondition: 'https://schema.org/UsedCondition'
                },
                sku: item.article_number,
                category: item.category.name
              }, null, 2)}
            </script>
          )}
        </Helmet>
        <Box sx={{ my: 4 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/items')}
            sx={{ mb: 2 }}
          >
            Back to List
          </Button>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {item.images.length > 0 ? (
                  <>
                    <Box
                      component="img"
                      src={`${item.images[0].image_path}`}
                      alt={item.name}
                      onClick={() => handleImageClick(item.images[0].image_path)}
                      sx={{
                        width: '100%',
                        height: '400px',
                        objectFit: 'cover',
                        borderRadius: 2,
                        cursor: 'pointer',
                        '&:hover': {
                          opacity: 0.9,
                        },
                      }}
                    />
                    {item.images.length > 1 && (
                      <Grid container spacing={1}>
                        {item.images.slice(1).map((image) => (
                          <Grid item xs={4} key={image.id}>
                            <Box
                              component="img"
                              src={`${image.image_path}`}
                              alt={`${item.name} - Image ${image.id}`}
                              onClick={() => handleImageClick(image.image_path)}
                              sx={{
                                width: '100%',
                                height: '100px',
                                objectFit: 'cover',
                                borderRadius: 2,
                                cursor: 'pointer',
                                '&:hover': {
                                  opacity: 0.9,
                                },
                              }}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    )}
                  </>
                ) : (
                  <Card>
                    <CardContent>
                      <Typography>No image available</Typography>
                    </CardContent>
                  </Card>
                )}
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h4" component="h1" gutterBottom>
                    {item.name}
                  </Typography>
                  <Typography variant="h5" color="primary" gutterBottom>
                    {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Contact us for price'}
                  </Typography>
                  {item.article_number && (
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      Article Number: {item.article_number}
                    </Typography>
                  )}
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Brand: {item.brand.name}
                  </Typography>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Category: {item.category.name}
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {item.description}
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => navigate(`/contact/${id}`)}
                  >
                    Contact Us About This Item
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <Dialog
            open={!!selectedImage}
            onClose={handleCloseModal}
            maxWidth="lg"
            fullWidth
          >
            <DialogContent sx={{ p: 0, position: 'relative' }}>
              <IconButton
                onClick={handleCloseModal}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: 'white',
                  bgcolor: 'rgba(0, 0, 0, 0.5)',
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                  },
                }}
              >
                <CloseIcon />
              </IconButton>
              {selectedImage && (
                <Box
                  component="img"
                  src={`${selectedImage}`}
                  alt="Full size"
                  sx={{
                    width: '100%',
                    height: 'auto',
                    maxHeight: '90vh',
                    objectFit: 'contain',
                  }}
                />
              )}
            </DialogContent>
          </Dialog>
        </Box>
      </Container>
    </HelmetProvider>
  );
};

export default ItemDetails;