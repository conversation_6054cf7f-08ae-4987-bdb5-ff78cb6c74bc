from sqlalchemy import create_engine, text
from app.models import Base
import sys

# Database connection
DATABASE_URL = "mysql+pymysql://lskuser:lskpassword@db:3306/lsktrading?charset=utf8mb4"
engine = create_engine(DATABASE_URL)

def reset_database():
    try:
        # Drop all tables
        print("Dropping existing tables...")
        Base.metadata.drop_all(bind=engine)
        
        # Create all tables
        print("Creating tables...")
        Base.metadata.create_all(bind=engine)
        
        # Verify tables were created
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print("\nTables created:")
            for table in tables:
                print(f"- {table[0]}")
                
        print("\nDatabase reset and initialized successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    reset_database() 