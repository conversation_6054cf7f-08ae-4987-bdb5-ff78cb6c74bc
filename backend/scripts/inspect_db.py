from sqlalchemy import create_engine, text
import sys

# Database connection
DATABASE_URL = "mysql+pymysql://lskuser:lskpassword@db:3306/lsktrading?charset=utf8mb4"
engine = create_engine(DATABASE_URL)

def inspect_database():
    try:
        with engine.connect() as conn:
            # Check tables
            print("\nChecking tables...")
            result = conn.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print("Tables found:")
            for table in tables:
                print(f"- {table[0]}")
                
            # Check users table structure
            print("\nChecking users table structure...")
            result = conn.execute(text("DESCRIBE users"))
            columns = result.fetchall()
            print("Users table columns:")
            for col in columns:
                print(f"- {col[0]} ({col[1]})")
                
            # Check if any users exist
            print("\nChecking for users...")
            result = conn.execute(text("SELECT COUNT(*) FROM users"))
            count = result.scalar()
            print(f"Number of users: {count}")
            
            if count > 0:
                result = conn.execute(text("SELECT * FROM users"))
                users = result.fetchall()
                print("\nUsers found:")
                for user in users:
                    print(f"ID: {user[0]}, Username: {user[1]}, Email: {user[3]}, Is Admin: {user[4]}")
                    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    inspect_database() 