from sqlalchemy.orm import Session
from . import models, schemas
from passlib.context import CryptContext
import sqlalchemy.orm

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def create_user(db: Session, user: schemas.UserCreate):
    db_user = models.User(
        username=user.username,
        email=user.email,
        password_hash=get_password_hash(user.password)
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def get_user_by_username(db: Session, username: str):
    return db.query(models.User).filter(models.User.username == username).first()

def create_brand(db: Session, brand: schemas.BrandCreate):
    db_brand = models.Brand(name=brand.name)
    db.add(db_brand)
    db.commit()
    db.refresh(db_brand)
    return db_brand

def get_brands(db: Session):
    return db.query(models.Brand).all()

def get_brand(db: Session, brand_id: int):
    return db.query(models.Brand).filter(models.Brand.id == brand_id).first()

def update_brand(db: Session, brand_id: int, brand: schemas.BrandCreate):
    db_brand = get_brand(db, brand_id)
    if db_brand:
        db_brand.name = brand.name
        db.commit()
        db.refresh(db_brand)
    return db_brand

def delete_brand(db: Session, brand_id: int):
    db_brand = get_brand(db, brand_id)
    if db_brand:
        db.delete(db_brand)
        db.commit()
    return db_brand

def create_category(db: Session, category: schemas.CategoryCreate):
    db_category = models.Category(name=category.name)
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category

def get_categories(db: Session):
    return db.query(models.Category).options(
        sqlalchemy.orm.joinedload(models.Category.subcategories)
    ).all()

def get_category(db: Session, category_id: int):
    return db.query(models.Category).options(
        sqlalchemy.orm.joinedload(models.Category.subcategories)
    ).filter(models.Category.id == category_id).first()

def update_category(db: Session, category_id: int, category: schemas.CategoryCreate):
    db_category = get_category(db, category_id)
    if db_category is None:
        return None

    db_category.name = category.name
    db.commit()
    db.refresh(db_category)
    return db_category

def delete_category(db: Session, category_id: int):
    db_category = get_category(db, category_id)
    if db_category is None:
        return None

    db.delete(db_category)
    db.commit()
    return True

def create_item(db: Session, item: schemas.ItemCreate):
    db_item = models.Item(
        name=item.name,
        description=item.description,  # Can now be None
        article_number=item.article_number,
        price=item.price,
        is_used=item.is_used,
        active=item.active,
        quantity=item.quantity,
        category_id=item.category_id,
        subcategory_id=item.subcategory_id,
        brand_id=item.brand_id
    )
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

def get_items(db: Session, skip: int = 0, limit: int = 100, active_only: bool = False):
    query = db.query(models.Item).options(
        sqlalchemy.orm.joinedload(models.Item.category),
        sqlalchemy.orm.joinedload(models.Item.subcategory),
        sqlalchemy.orm.joinedload(models.Item.brand),
        sqlalchemy.orm.joinedload(models.Item.images)
    )

    if active_only:
        query = query.filter(models.Item.active == True)

    return query.offset(skip).limit(limit).all()

def get_item(db: Session, item_id: int, active_only: bool = False):
    query = db.query(models.Item).options(
        sqlalchemy.orm.joinedload(models.Item.category),
        sqlalchemy.orm.joinedload(models.Item.subcategory),
        sqlalchemy.orm.joinedload(models.Item.brand),
        sqlalchemy.orm.joinedload(models.Item.images)
    ).filter(models.Item.id == item_id)

    if active_only:
        query = query.filter(models.Item.active == True)

    return query.first()

def update_item(db: Session, item_id: int, item: schemas.ItemUpdate):
    db_item = get_item(db, item_id)
    if db_item is None:
        return None

    update_data = item.dict(exclude_unset=True)
    for key, value in update_data.items():
        # Handle boolean fields explicitly
        if key in ['is_used', 'active'] and value is not None:
            setattr(db_item, key, value)
        elif value is not None:  # Only update non-None values for other fields
            setattr(db_item, key, value)

    db.commit()
    db.refresh(db_item)
    return db_item

def delete_item(db: Session, item_id: int):
    db_item = get_item(db, item_id)
    if db_item is None:
        return None

    db.delete(db_item)
    db.commit()
    return True

def create_item_image(db: Session, item_id: int, image_path: str):
    db_image = models.ItemImage(item_id=item_id, image_path=image_path)
    db.add(db_image)
    db.commit()
    db.refresh(db_image)
    return db_image

def delete_item_image(db: Session, image_id: int):
    db_image = db.query(models.ItemImage).filter(models.ItemImage.id == image_id).first()
    if db_image is None:
        return None

    db.delete(db_image)
    db.commit()
    return True

def get_subcategories_by_category(db: Session, category_id: int):
    return db.query(models.SubCategory).filter(models.SubCategory.category_id == category_id).all()

def create_subcategory(db: Session, subcategory: schemas.SubCategoryCreate):
    db_subcategory = models.SubCategory(
        name=subcategory.name,
        category_id=subcategory.category_id
    )
    db.add(db_subcategory)
    db.commit()
    db.refresh(db_subcategory)
    return db_subcategory

def update_subcategory(db: Session, subcategory_id: int, subcategory: schemas.SubCategoryCreate):
    db_subcategory = db.query(models.SubCategory).filter(models.SubCategory.id == subcategory_id).first()
    if db_subcategory is None:
        return None

    db_subcategory.name = subcategory.name
    db_subcategory.category_id = subcategory.category_id
    db.commit()
    db.refresh(db_subcategory)
    return db_subcategory

def delete_subcategory(db: Session, subcategory_id: int):
    db_subcategory = db.query(models.SubCategory).filter(models.SubCategory.id == subcategory_id).first()
    if db_subcategory is None:
        return None

    db.delete(db_subcategory)
    db.commit()
    return True

# News CRUD functions
def get_news(db: Session):
    return db.query(models.News).order_by(models.News.updated_at.desc()).first()

def create_news(db: Session, news: schemas.NewsCreate):
    db_news = models.News(**news.dict())
    db.add(db_news)
    db.commit()
    db.refresh(db_news)
    return db_news

def update_news(db: Session, news_id: int, news_update: schemas.NewsUpdate):
    db_news = db.query(models.News).filter(models.News.id == news_id).first()
    if db_news:
        for key, value in news_update.dict(exclude_unset=True).items():
            setattr(db_news, key, value)
        db.commit()
        db.refresh(db_news)
    return db_news

# Random items for "Today's Pick"
def get_random_active_items(db: Session, limit: int = 2):
    return db.query(models.Item).options(
        sqlalchemy.orm.joinedload(models.Item.category),
        sqlalchemy.orm.joinedload(models.Item.subcategory),
        sqlalchemy.orm.joinedload(models.Item.brand),
        sqlalchemy.orm.joinedload(models.Item.images)
    ).filter(models.Item.active == True).order_by(sqlalchemy.func.random()).limit(limit).all()