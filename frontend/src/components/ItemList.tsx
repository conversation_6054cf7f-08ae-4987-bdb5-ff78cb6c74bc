import React from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Box,
  Chip,
  Link,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

interface Item {
  id: number;
  name: string;
  description: string;
  price: number;
  article_number: string;
  quantity: number;
  is_used: boolean;
  active: boolean;
  category: {
    id: number;
    name: string;
  };
  subcategory?: {
    id: number;
    name: string;
  };
  brand: {
    id: number;
    name: string;
  };
  images: {
    id: number;
    image_path: string;
  }[];
}

interface ItemListProps {
  items: Item[];
}

const ItemList: React.FC<ItemListProps> = ({ items }) => {
  return (
    <Grid container spacing={3}>
      {items.map((item: Item) => (
        <Grid item xs={12} sm={6} md={4} key={item.id}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Link
              component={RouterLink}
              to={`/items/${item.id}`}
              sx={{ textDecoration: 'none' }}
            >
              <CardMedia
                component="img"
                height="200"
                image={item.images[0]?.image_path || '/placeholder.jpg'}
                alt={item.name}
                sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
              />
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="h6" component="div" noWrap>
                    {item.name}
                  </Typography>
                  <Typography variant="h6" color="primary">
                    {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Contact us'}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  {item.article_number}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  {item.description.length > 100
                    ? `${item.description.substring(0, 100)}...`
                    : item.description}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                  <Chip
                    label={item.category.name}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                  {item.subcategory && (
                    <Chip
                      label={item.subcategory.name}
                      size="small"
                      color="secondary"
                      variant="outlined"
                    />
                  )}
                  <Chip
                    label={item.brand.name}
                    size="small"
                    color="default"
                    variant="outlined"
                  />
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {item.is_used && (
                    <Chip
                      label="Used"
                      size="small"
                      color="warning"
                      variant="outlined"
                    />
                  )}
                  {!item.active && (
                    <Chip
                      label="Inactive"
                      size="small"
                      color="error"
                      variant="outlined"
                    />
                  )}
                  <Chip
                    label={`Qty: ${item.quantity}`}
                    size="small"
                    color="info"
                    variant="outlined"
                  />
                </Box>
              </CardContent>
            </Link>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default ItemList;