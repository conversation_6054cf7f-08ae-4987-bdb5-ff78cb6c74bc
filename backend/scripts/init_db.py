import os
import sys
import pymysql
from sqlalchemy import create_engine, inspect, text
from app.models import Base
from app.database import DATABASE_URL

def test_db_connection():
    try:
        print("Testing direct database connection...")
        conn = pymysql.connect(
            host='db',
            user=os.getenv('MYSQL_USER', 'lskuser'),
            password=os.getenv('MYSQL_PASSWORD', 'lskpassword'),
            database=os.getenv('MYSQL_DATABASE', 'lsktrading')
        )
        with conn.cursor() as cursor:
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print("\nExisting tables:")
            for table in tables:
                print(f"- {table[0]}")
        conn.close()
        return True
    except Exception as e:
        print(f"Error testing database connection: {str(e)}")
        return False

def init_db():
    try:
        print(f"\nConnecting to database with URL: {DATABASE_URL}")
        
        # Test direct connection first
        if not test_db_connection():
            print("Failed to connect to database directly")
            sys.exit(1)
        
        # Create engine with explicit charset
        engine = create_engine(
            DATABASE_URL,
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={"charset": "utf8mb4"}
        )
        
        # Test SQLAlchemy connection
        print("\nTesting SQLAlchemy connection...")
        with engine.connect() as connection:
            result = connection.execute(text("SELECT DATABASE()"))
            print(f"SQLAlchemy connected to database: {result.scalar()}")
        
        # Create all tables
        print("\nCreating database tables...")
        Base.metadata.create_all(bind=engine)
        
        # Verify tables were created
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print("\nCreated tables:")
        for table in tables:
            print(f"- {table}")
            
        if not tables:
            print("WARNING: No tables were created!")
            sys.exit(1)
            
        print("\nDatabase initialization completed successfully")
    except Exception as e:
        print(f"\nError during database initialization: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        sys.exit(1)

if __name__ == "__main__":
    init_db() 