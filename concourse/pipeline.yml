---
resources:
  - name: lsktrading-git
    type: git
    source:
      uri: https://github.com/marwen01/lsktrading.git
      username: marwen01
      icon: github
      password: ****************************************
      branch: master


jobs:
  - name: build-and-push
    plan:
      - get: lsktrading-git
        trigger: true

      - task: build-backend
        privileged: true
        config:
          platform: linux
          image_resource:
            type: docker-image
            source:
              repository: docker
              tag: dind
          inputs:
            - name: lsktrading-git
          caches:
            - path: cache
          run:
            path: sh
            args:
              - -exc
              - |
                # Start Docker daemon
                dockerd --insecure-registry 192.168.100.22:5000 --data-root /scratch/docker &
                sleep 5  # Wait for Docker daemon to start

                # Build and push
                cd lsktrading-git/backend
                docker build --progress=plain -f Dockerfile.prod -t 192.168.100.22:5000/lsktrading/backend:latest .
                docker push 192.168.100.22:5000/lsktrading/backend:latest

      - task: build-frontend
        privileged: true
        config:
          platform: linux
          image_resource:
            type: docker-image
            source:
              repository: docker
              tag: dind
          inputs:
            - name: lsktrading-git
          caches:
            - path: cache
          run:
            path: sh
            args:
              - -exc
              - |
                # Start Docker daemon
                dockerd --insecure-registry 192.168.100.22:5000 --data-root /scratch/docker &
                sleep 5  # Wait for Docker daemon to start

                # Build and push
                cd lsktrading-git/frontend
                docker build --progress=plain -f Dockerfile.prod -t 192.168.100.22:5000/lsktrading/frontend:latest .
                docker push 192.168.100.22:5000/lsktrading/frontend:latest