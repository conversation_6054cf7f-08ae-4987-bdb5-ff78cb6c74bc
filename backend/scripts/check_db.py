from sqlalchemy import create_engine, text
import sys

# Database connection
DATABASE_URL = "mysql+pymysql://lskuser:lskpassword@db:3306/lsktrading?charset=utf8mb4"

def check_database():
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as conn:
            # Check if we can connect
            print("Database connection successful!")
            
            # Check tables
            result = conn.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print("\nExisting tables:")
            for table in tables:
                print(f"- {table[0]}")
                
            # Check users table contents
            print("\nUsers table contents:")
            result = conn.execute(text("SELECT * FROM users"))
            users = result.fetchall()
            if not users:
                print("No users found in the table!")
            else:
                for user in users:
                    print(f"User ID: {user.id}, Username: {user.username}, Email: {user.email}")
                    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    check_database() 