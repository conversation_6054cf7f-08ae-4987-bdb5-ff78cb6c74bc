import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import {
    Box,
    Button,
    TextField,
    Typography,
    Paper,
    Grid,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
} from '@mui/material';
import { Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import api from '../../api/axios';
import { useAuth } from '../../context/AuthContext';

interface SubCategory {
    id: number;
    name: string;
    category_id: number;
}

export const SubCategoryForm: React.FC = () => {
    const { categoryId } = useParams<{ categoryId: string }>();
    const { isAdmin } = useAuth();
    const [subcategories, setSubcategories] = useState<SubCategory[]>([]);
    const [newSubCategory, setNewSubCategory] = useState('');
    const [editingSubCategory, setEditingSubCategory] = useState<SubCategory | null>(null);
    const [openDialog, setOpenDialog] = useState(false);

    const fetchSubcategories = useCallback(async () => {
        try {
            const response = await api.get(`/api/categories/${categoryId}/subcategories/`);
            setSubcategories(response.data);
        } catch (error) {
            console.error('Error fetching subcategories:', error);
        }
    }, [categoryId]);

    useEffect(() => {
        fetchSubcategories();
    }, [fetchSubcategories]);

    const handleCreate = async () => {
        if (!newSubCategory.trim()) return;

        try {
            const response = await api.post(`/api/categories/${categoryId}/subcategories/`, {
                name: newSubCategory,
                category_id: parseInt(categoryId!)
            });
            setSubcategories([...subcategories, response.data]);
            setNewSubCategory('');
        } catch (error) {
            console.error('Error creating subcategory:', error);
        }
    };

    const handleUpdate = async () => {
        if (!editingSubCategory || !editingSubCategory.name.trim()) return;

        try {
            const response = await api.put(`/api/categories/${categoryId}/subcategories/${editingSubCategory.id}`, {
                name: editingSubCategory.name,
                category_id: parseInt(categoryId!)
            });
            setSubcategories(subcategories.map(sc => 
                sc.id === editingSubCategory.id ? response.data : sc
            ));
            setEditingSubCategory(null);
            setOpenDialog(false);
        } catch (error) {
            console.error('Error updating subcategory:', error);
        }
    };

    const handleDelete = async (id: number) => {
        try {
            await api.delete(`/api/categories/${categoryId}/subcategories/${id}`);
            setSubcategories(subcategories.filter(sc => sc.id !== id));
        } catch (error) {
            console.error('Error deleting subcategory:', error);
        }
    };

    if (!isAdmin) {
        return (
            <Box p={3}>
                <Typography variant="h6" color="error">
                    You don't have permission to access this page.
                </Typography>
            </Box>
        );
    }

    return (
        <Box p={3}>
            <Typography variant="h4" gutterBottom>
                Manage Subcategories
            </Typography>

            <Paper sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={8}>
                        <TextField
                            fullWidth
                            label="New Subcategory"
                            value={newSubCategory}
                            onChange={(e) => setNewSubCategory(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleCreate()}
                        />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                        <Button
                            fullWidth
                            variant="contained"
                            color="primary"
                            onClick={handleCreate}
                        >
                            Add Subcategory
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            <Grid container spacing={2}>
                {subcategories.map((subcategory) => (
                    <Grid item xs={12} sm={6} md={4} key={subcategory.id}>
                        <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Typography>{subcategory.name}</Typography>
                            <Box>
                                <IconButton
                                    color="primary"
                                    onClick={() => {
                                        setEditingSubCategory(subcategory);
                                        setOpenDialog(true);
                                    }}
                                >
                                    <EditIcon />
                                </IconButton>
                                <IconButton
                                    color="error"
                                    onClick={() => handleDelete(subcategory.id)}
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Box>
                        </Paper>
                    </Grid>
                ))}
            </Grid>

            <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
                <DialogTitle>Edit Subcategory</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="Name"
                        fullWidth
                        value={editingSubCategory?.name || ''}
                        onChange={(e) => setEditingSubCategory({
                            ...editingSubCategory!,
                            name: e.target.value
                        })}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
                    <Button onClick={handleUpdate} color="primary">
                        Save
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}; 