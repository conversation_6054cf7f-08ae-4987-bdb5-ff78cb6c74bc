from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from .routers.auth import router as auth_router
from .routers.items import router as items_router
from .routers.brands import router as brands_router
from .routers.categories import router as categories_router
from .routers.contact import router as contact_router
from .routers.sitemap import router as sitemap_router
from fastapi.middleware.cors import CORSMiddleware

# Version constant - increment this with each release
API_VERSION = "1.1.0"

app = FastAPI(title="LSK Trading Maritime Catalogue", version=API_VERSION)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000","http://**************:8040"],  # MAYBE THIS NEEDS TO CHANGE IN PRODUCTION!!!
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount the uploads directory
app.mount("/uploads", StaticFiles(directory="/app/uploads"), name="uploads")

# Include routers (prefixes **without** trailing slashes)
app.include_router(auth_router, prefix="/api", tags=["auth"])
app.include_router(items_router, prefix="/api/items", tags=["items"])
app.include_router(brands_router, prefix="/api/brands", tags=["brands"])
app.include_router(categories_router, prefix="/api/categories", tags=["categories"])
app.include_router(contact_router, prefix="/api/contact", tags=["contact"])
app.include_router(sitemap_router, tags=["sitemap"])

# API version endpoint
@app.get("/api/version")
def get_api_version():
    return {"version": API_VERSION, "api": "LSK Trading Maritime Catalogue"}

@app.get("/")
def read_root():
    return {"message": "Welcome to LSK Trading Maritime Spare Parts Catalogue API", "version": API_VERSION}

@app.get("/version")
def get_version():
    return {"version": API_VERSION, "api": "LSK Trading Maritime Catalogue"}
