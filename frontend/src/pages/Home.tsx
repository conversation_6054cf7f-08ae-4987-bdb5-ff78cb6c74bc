import React from 'react';
import { Container, Typography, Box, Grid, Card, CardContent, CardMedia, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

const Home: React.FC = () => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h2" component="h1" gutterBottom align="center">
          Welcome to LSK Trading
        </Typography>
        <Typography variant="h5" component="h2" gutterBottom align="center" color="text.secondary">
          Your Trusted Maritime Spare Parts Supplier
        </Typography>
        
        <Box sx={{ my: 4 }}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h5" component="h2" gutterBottom>
                      Browse Our Catalogue
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Explore our extensive collection of maritime spare parts from leading manufacturers.
                    </Typography>
                  </Box>
                  <Button variant="contained" component={RouterLink} to="/items" sx={{ alignSelf: 'flex-start' }}>
                    View Items
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h5" component="h2" gutterBottom>
                      Contact Us
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Have questions about our products? Get in touch with our team.
                    </Typography>
                  </Box>
                  <Button variant="contained" component={RouterLink} to="/contact" sx={{ alignSelf: 'flex-start' }}>
                    Contact Form
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Container>
  );
};

export default Home; 