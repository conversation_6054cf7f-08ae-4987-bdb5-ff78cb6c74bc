import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Typography,
  IconButton,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import api from '../../api/axios';

interface Brand {
  id: number;
  name: string;
}

const ManageBrands: React.FC = () => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [formData, setFormData] = useState({
    name: '',
  });
  const [editingId, setEditingId] = useState<number | null>(null);

  useEffect(() => {
    fetchBrands();
  }, []);

  const fetchBrands = async () => {
    try {
      const response = await api.get('/brands/');
      setBrands(response.data);
    } catch (error) {
      console.error('Error fetching brands:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const brandData = {
        name: formData.name.trim()
      };
      
      if (!brandData.name) {
        console.error('Brand name cannot be empty');
        return;
      }

      console.log('Sending brand data:', brandData);
      
      if (editingId) {
        await api.put(`/brands/${editingId}/`, brandData);
      } else {
        await api.post('/brands/', brandData);
      }
      fetchBrands();
      setFormData({
        name: '',
      });
      setEditingId(null);
    } catch (error: any) {
      console.error('Error saving brand:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
    }
  };

  const handleEdit = (brand: Brand) => {
    setFormData({
      name: brand.name,
    });
    setEditingId(brand.id);
  };

  const handleDelete = async (id: number) => {
    try {
      await api.delete(`/brands/${id}/`);
      fetchBrands();
    } catch (error) {
      console.error('Error deleting brand:', error);
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        {editingId ? 'Edit Brand' : 'Add New Brand'}
      </Typography>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <Button type="submit" variant="contained" color="primary">
              {editingId ? 'Update Brand' : 'Add Brand'}
            </Button>
            {editingId && (
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => {
                  setFormData({ name: '' });
                  setEditingId(null);
                }}
                sx={{ ml: 2 }}
              >
                Cancel
              </Button>
            )}
          </Grid>
        </Grid>
      </form>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Existing Brands
        </Typography>
        <Grid container spacing={2}>
          {brands.map((brand) => (
            <Grid item xs={12} sm={6} md={4} key={brand.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6">{brand.name}</Typography>
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                    <IconButton onClick={() => handleEdit(brand)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(brand.id)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default ManageBrands; 