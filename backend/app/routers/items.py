from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil
from ..deps import get_db
from ..crud import (
    get_items,
    get_item,
    create_item,
    update_item,
    delete_item,
    create_item_image,
    delete_item_image,
)
from ..models import ItemImage, Item
from ..schemas import ItemCreate, ItemUpdate, ItemOut, ItemImageOut

router = APIRouter()

# Ensure upload directory exists
UPLOAD_DIR = "/app/uploads"
if not os.path.exists(UPLOAD_DIR):
    os.makedirs(UPLOAD_DIR)

@router.get("", response_model=List[ItemOut])
@router.get("/", response_model=List[ItemOut])
def read_items(
    skip: int = 0,
    limit: int = 100,
    active_only: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    # Convert active_only to boolean, defaulting to False if None
    active_filter = bool(active_only) if active_only is not None else False
    items = get_items(db, skip=skip, limit=limit, active_only=active_filter)
    return items

@router.get("/{item_id}", response_model=ItemOut)
@router.get("/{item_id}/", response_model=ItemOut)
def read_item(item_id: int, active_only: bool = False, db: Session = Depends(get_db)):
    # Convert active_only to boolean to ensure proper filtering
    active_filter = bool(active_only)
    db_item = get_item(db, item_id=item_id, active_only=active_filter)
    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")
    return db_item


@router.post("", response_model=ItemOut)  # no trailing slash
@router.post("/", response_model=ItemOut)  # no trailing slash
def create_new_item(item: ItemCreate, db: Session = Depends(get_db)):
    return create_item(db=db, item=item)

@router.put("/{item_id}", response_model=ItemOut)
@router.put("/{item_id}/", response_model=ItemOut)
def update_existing_item(item_id: int, item: ItemUpdate, db: Session = Depends(get_db)):
    db_item = get_item(db, item_id=item_id)
    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")
    return update_item(db=db, item_id=item_id, item=item)

@router.delete("/{item_id}")
@router.delete("/{item_id}/")
def delete_existing_item(item_id: int, db: Session = Depends(get_db)):
    db_item = get_item(db, item_id=item_id)
    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")
    delete_item(db=db, item_id=item_id)
    return {"message": "Item deleted successfully"}

@router.post("/{item_id}/images", response_model=List[ItemImageOut])
@router.post("/{item_id}/images/", response_model=List[ItemImageOut])
async def upload_item_images(
    item_id: int,
    images: List[UploadFile] = File(...),
    db: Session = Depends(get_db)
):
    db_item = get_item(db, item_id=item_id)
    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")

    saved_images = []
    for image in images:
        # Create a unique filename
        file_extension = os.path.splitext(image.filename)[1]
        unique_filename = f"{item_id}_{os.urandom(8).hex()}{file_extension}"
        file_path = os.path.join(UPLOAD_DIR, unique_filename)

        # Save the file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)

        # Create database record
        db_image = create_item_image(
            db=db,
            item_id=item_id,
            image_path=f"/uploads/{unique_filename}"
        )
        saved_images.append(db_image)

    return saved_images

@router.delete("/images/{image_id}")
@router.delete("/images/{image_id}/")
def delete_item_image_endpoint(image_id: int, db: Session = Depends(get_db)):
    db_image = db.query(ItemImage).filter(ItemImage.id == image_id).first()
    if db_image is None:
        raise HTTPException(status_code=404, detail="Image not found")

    # Delete the file
    file_path = os.path.join(UPLOAD_DIR, os.path.basename(db_image.image_path))
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete the database record
    delete_item_image(db=db, image_id=image_id)
    return {"message": "Image deleted successfully"}

@router.get("/{item_id}/seo")
async def get_item_seo(item_id: int, db: Session = Depends(get_db)):
    item = db.query(Item).filter(Item.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="Item not found")

    # Prepare structured data
    structured_data = {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": item.name,
        "image": [f"{os.getenv('FRONTEND_URL', 'http://localhost:3000')}{img.image_path}" for img in item.images],
        "description": item.description,
        "sku": item.article_number,
        "brand": {
            "@type": "Brand",
            "name": item.brand.name
        },
        "offers": {
            "@type": "Offer",
            "url": f"{os.getenv('FRONTEND_URL', 'http://localhost:3000')}/items/{item.id}",
            "priceCurrency": "USD",
            "price": str(item.price),
            "availability": "https://schema.org/InStock"
        }
    }

    return {
        "title": f"{item.name} {item.article_number if item.article_number else ''} | LSK Trading",
        "description": item.description,
        "name": item.name,
        "article_number": item.article_number,
        "structured_data": structured_data
    }
