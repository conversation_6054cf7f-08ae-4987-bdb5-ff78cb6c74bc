from fastapi import APIRouter, Depends
from fastapi.responses import Response
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import Item
from datetime import datetime
import xml.etree.ElementTree as ET

router = APIRouter()

def generate_sitemap_xml(db: Session) -> str:
    # Create XML structure
    urlset = ET.Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
    
    # Add items listing page
    url = ET.SubElement(urlset, "url")
    ET.SubElement(url, "loc").text = "https://lsktrading.com/items"
    ET.SubElement(url, "lastmod").text = datetime.now().strftime("%Y-%m-%d")
    ET.SubElement(url, "changefreq").text = "daily"
    ET.SubElement(url, "priority").text = "1.0"
    
    # Add individual item pages
    items = db.query(Item).all()
    for item in items:
        url = ET.SubElement(urlset, "url")
        ET.SubElement(url, "loc").text = f"https://lsktrading.com/items/{item.id}"
        ET.SubElement(url, "lastmod").text = datetime.now().strftime("%Y-%m-%d")
        ET.SubElement(url, "changefreq").text = "weekly"
        ET.SubElement(url, "priority").text = "0.8"
    
    # Convert to string
    xml_str = ET.tostring(urlset, encoding="unicode", method="xml")
    return f'<?xml version="1.0" encoding="UTF-8"?>\n{xml_str}'

@router.get("/sitemap.xml")
async def get_sitemap(db: Session = Depends(get_db)):
    xml_content = generate_sitemap_xml(db)
    return Response(content=xml_content, media_type="application/xml") 