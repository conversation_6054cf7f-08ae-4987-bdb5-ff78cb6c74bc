import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Typography,
  IconButton,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import api from '../../api/axios';

interface Category {
  id: number;
  name: string;
}

const ManageCategories: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [formData, setFormData] = useState({
    name: '',
  });
  const [editingId, setEditingId] = useState<number | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const categoryData = {
        name: formData.name.trim()
      };
      
      if (!categoryData.name) {
        console.error('Category name cannot be empty');
        return;
      }

      console.log('Sending category data:', categoryData);
      
      if (editingId) {
        await api.put(`/categories/${editingId}/`, categoryData);
      } else {
        await api.post('/categories/', categoryData);
      }
      fetchCategories();
      setFormData({
        name: '',
      });
      setEditingId(null);
    } catch (error: any) {
      console.error('Error saving category:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
    }
  };

  const handleEdit = (category: Category) => {
    setFormData({
      name: category.name,
    });
    setEditingId(category.id);
  };

  const handleDelete = async (id: number) => {
    try {
      await api.delete(`/categories/${id}`);
      fetchCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        {editingId ? 'Edit Category' : 'Add New Category'}
      </Typography>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <Button type="submit" variant="contained" color="primary">
              {editingId ? 'Update Category' : 'Add Category'}
            </Button>
            {editingId && (
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => {
                  setFormData({
                    name: '',
                  });
                  setEditingId(null);
                }}
                sx={{ ml: 2 }}
              >
                Cancel
              </Button>
            )}
          </Grid>
        </Grid>
      </form>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Existing Categories
        </Typography>
        <Grid container spacing={2}>
          {categories.map((category) => (
            <Grid item xs={12} sm={6} md={4} key={category.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6">{category.name}</Typography>
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                    <IconButton onClick={() => handleEdit(category)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(category.id)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default ManageCategories; 