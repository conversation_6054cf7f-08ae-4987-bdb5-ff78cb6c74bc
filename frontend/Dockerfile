# Dev stage only (no builder, no Nginx)
FROM node:18-alpine

WORKDIR /app

# Install dependencies early to leverage Docker cache
COPY package*.json ./
RUN npm install

# Copy source (this will be overridden by bind mount anyway)
COPY . .



COPY .env.development .env
# Environment vars like VITE_API_URL will come from docker-compose

EXPOSE 3000

# Start Vite/React dev server with hot reload
CMD ["npm", "run", "dev"]