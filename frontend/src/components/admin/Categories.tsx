import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
    Box,
    Button,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Typography
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    SubdirectoryArrowRight as SubdirectoryIcon
} from '@mui/icons-material';
import api from '../../api/axios';
import { useAuth } from '../../context/AuthContext';

interface Category {
    id: number;
    name: string;
}

export const Categories: React.FC = () => {
    const { isAdmin } = useAuth();
    const navigate = useNavigate();
    const [categories, setCategories] = useState<Category[]>([]);
    const [newCategory, setNewCategory] = useState('');
    const [editingCategory, setEditingCategory] = useState<Category | null>(null);
    const [openDialog, setOpenDialog] = useState(false);

    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        try {
            const response = await api.get('/categories/');
            setCategories(response.data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const handleCreate = async () => {
        if (!newCategory.trim()) return;

        try {
            const response = await api.post('/categories/', { name: newCategory });
            setCategories([...categories, response.data]);
            setNewCategory('');
        } catch (error) {
            console.error('Error creating category:', error);
        }
    };

    const handleUpdate = async () => {
        if (!editingCategory || !editingCategory.name.trim()) return;

        try {
            const response = await api.put(`/categories/${editingCategory.id}`, {
                name: editingCategory.name
            });
            setCategories(categories.map(cat => 
                cat.id === editingCategory.id ? response.data : cat
            ));
            setEditingCategory(null);
            setOpenDialog(false);
        } catch (error) {
            console.error('Error updating category:', error);
        }
    };

    const handleDelete = async (id: number) => {
        try {
            await api.delete(`/categories/${id}`);
            setCategories(categories.filter(cat => cat.id !== id));
        } catch (error) {
            console.error('Error deleting category:', error);
        }
    };

    const handleManageSubcategories = (categoryId: number) => {
        navigate(`/admin/categories/${categoryId}/subcategories`);
    };

    if (!isAdmin) {
        return (
            <Box p={3}>
                <Typography variant="h6" color="error">
                    You don't have permission to access this page.
                </Typography>
            </Box>
        );
    }

    return (
        <Box p={3}>
            <Typography variant="h4" gutterBottom>
                Manage Categories
            </Typography>

            <Paper sx={{ p: 2, mb: 2 }}>
                <Box display="flex" gap={2}>
                    <TextField
                        fullWidth
                        label="New Category"
                        value={newCategory}
                        onChange={(e) => setNewCategory(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleCreate()}
                    />
                    <Button
                        variant="contained"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={handleCreate}
                    >
                        Add
                    </Button>
                </Box>
            </Paper>

            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Name</TableCell>
                            <TableCell align="right">Actions</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {categories.map((category) => (
                            <TableRow key={category.id}>
                                <TableCell>{category.name}</TableCell>
                                <TableCell align="right">
                                    <IconButton
                                        color="primary"
                                        onClick={() => handleManageSubcategories(category.id)}
                                    >
                                        <SubdirectoryIcon />
                                    </IconButton>
                                    <IconButton
                                        color="primary"
                                        onClick={() => {
                                            setEditingCategory(category);
                                            setOpenDialog(true);
                                        }}
                                    >
                                        <EditIcon />
                                    </IconButton>
                                    <IconButton
                                        color="error"
                                        onClick={() => handleDelete(category.id)}
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
                <DialogTitle>Edit Category</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="Name"
                        fullWidth
                        value={editingCategory?.name || ''}
                        onChange={(e) => setEditingCategory({
                            ...editingCategory!,
                            name: e.target.value
                        })}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
                    <Button onClick={handleUpdate} color="primary">
                        Save
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default Categories; 