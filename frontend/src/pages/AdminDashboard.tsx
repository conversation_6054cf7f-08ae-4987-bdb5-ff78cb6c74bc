import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography } from '@mui/material';
import ManageItems from '../components/admin/ManageItems';
import ManageBrands from '../components/admin/ManageBrands';
import ManageCategories from '../components/admin/ManageCategories';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AdminDashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Items" />
          <Tab label="Brands" />
          <Tab label="Categories" />
        </Tabs>
      </Box>
      <TabPanel value={tabValue} index={0}>
        <ManageItems />
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
        <ManageBrands />
      </TabPanel>
      <TabPanel value={tabValue} index={2}>
        <ManageCategories />
      </TabPanel>
    </Box>
  );
};

export default AdminDashboard; 