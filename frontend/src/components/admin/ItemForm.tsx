import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  SelectChangeEvent,
} from '@mui/material';
import { Delete as DeleteIcon, AddPhotoAlternate as AddPhotoIcon } from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import api, { imageApi } from '../../api/axios';

interface Item {
  id?: number;
  name: string;
  description: string;
  price: number;
  article_number?: string;
  brand_id: number;
  category_id: number;
  subcategory_id?: number;
  images: {
    id: number;
    image_path: string;
  }[];
  quantity: number;
  is_used: boolean;
  active: boolean;
}

interface Brand {
  id: number;
  name: string;
}

interface Category {
  id: number;
  name: string;
  subcategories: SubCategory[];
}

interface SubCategory {
  id: number;
  name: string;
}

const ItemForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [item, setItem] = useState<Item>({
    name: '',
    description: '',
    price: 0,
    article_number: '',
    brand_id: 0,
    category_id: 0,
    images: [],
    quantity: 0,
    is_used: true,
    active: true,
  });
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<SubCategory[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<number | null>(null);

  useEffect(() => {
    fetchBrandsAndCategories();
    if (id && id !== 'new') {
      fetchItem();
    }
  }, [id]);

  const fetchBrandsAndCategories = async () => {
    try {
      const [brandsRes, categoriesRes] = await Promise.all([
        api.get('/brands'),
        api.get('/categories'),
      ]);
      setBrands(brandsRes.data);
      setCategories(categoriesRes.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const fetchItem = async () => {
    try {
      const response = await api.get(`/items/${id}`);
      setItem(response.data);
      if (response.data.category_id) {
        fetchSubcategories(response.data.category_id);
      }
    } catch (error) {
      console.error('Error fetching item:', error);
    }
  };

  const fetchSubcategories = async (categoryId: number) => {
    try {
      const response = await api.get(`/categories/${categoryId}/subcategories/`);
      setSubcategories(response.data);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      setSubcategories([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (id && id !== 'new') {
        await api.put(`/items/${id}/`, item);
      } else {
        await api.post('/items/', item);
      }
      navigate('/admin/items');
    } catch (error) {
      console.error('Error saving item:', error);
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    // If this is a new item (no ID), save it first
    if (!id || id === 'new') {
      try {
        const response = await api.post('/items/', item);
        const newItemId = response.data.id;
        navigate(`/admin/items/${newItemId}`);
        // Now that we have an ID, proceed with image upload
        const formData = new FormData();
        for (let i = 0; i < files.length; i++) {
          formData.append('images', files[i]);
        }
        const imageResponse = await api.post(`/items/${newItemId}/images/`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        setItem(prev => ({
          ...prev,
          images: [...prev.images, ...imageResponse.data],
        }));
      } catch (error) {
        console.error('Error saving item or uploading images:', error);
      }
    } else {
      // Existing item, proceed with image upload
      const formData = new FormData();
      for (let i = 0; i < files.length; i++) {
        formData.append('images', files[i]);
      }
      try {
        const response = await api.post(`/items/${id}/images/`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        setItem(prev => ({
          ...prev,
          images: [...prev.images, ...response.data],
        }));
      } catch (error) {
        console.error('Error uploading images:', error);
      }
    }
  };

  const handleDeleteImage = async (imageId: number) => {
    setImageToDelete(imageId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteImage = async () => {
    if (imageToDelete) {
      try {
        await api.delete(`/items/images/${imageToDelete}/`);
        setItem(prev => ({
          ...prev,
          images: prev.images.filter(img => img.id !== imageToDelete),
        }));
      } catch (error) {
        console.error('Error deleting image:', error);
      }
    }
    setDeleteDialogOpen(false);
    setImageToDelete(null);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<number>) => {
    const { name, value } = e.target;
    setItem(prev => ({
      ...prev,
      [name]: value,
    }));

    if (name === 'category_id') {
      setItem(prev => ({
        ...prev,
        subcategory_id: 0,
      }));
      if (value) {
        fetchSubcategories(Number(value));
      } else {
        setSubcategories([]);
      }
    }
  };

  const handleSwitchChange = (name: 'is_used' | 'active', checked: boolean) => {
    setItem(prev => ({
      ...prev,
      [name]: checked,
    }));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {id === 'new' ? 'Create New Item' : 'Edit Item'}
      </Typography>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={item.name}
              onChange={handleChange}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Article Number"
              name="article_number"
              value={item.article_number}
              onChange={handleChange}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              name="description"
              multiline
              rows={4}
              value={item.description}
              onChange={handleChange}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Price"
              name="price"
              type="number"
              value={item.price}
              onChange={handleChange}
              required
              inputProps={{ step: '0.01' }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Quantity"
              name="quantity"
              type="number"
              value={item.quantity}
              onChange={handleChange}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Brand</InputLabel>
              <Select
                name="brand_id"
                value={item.brand_id || ''}
                onChange={handleChange}
                label="Brand"
              >
                {brands.map((brand) => (
                  <MenuItem key={brand.id} value={brand.id}>
                    {brand.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Category</InputLabel>
              <Select
                name="category_id"
                value={item.category_id || ''}
                onChange={handleChange}
                label="Category"
              >
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Subcategory</InputLabel>
              <Select
                name="subcategory_id"
                value={item.subcategory_id || 0}
                onChange={handleChange}
                label="Subcategory"
                disabled={!item.category_id}
              >
                <MenuItem value={0}>
                  <em>None</em>
                </MenuItem>
                {subcategories.map((subcategory) => (
                  <MenuItem key={subcategory.id} value={subcategory.id}>
                    {subcategory.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={item.is_used}
                  onChange={(e) => handleSwitchChange('is_used', e.target.checked)}
                  name="is_used"
                />
              }
              label="Used Item"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={item.active}
                  onChange={(e) => handleSwitchChange('active', e.target.checked)}
                  name="active"
                />
              }
              label="Active"
            />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="image-upload"
                type="file"
                multiple
                onChange={handleImageUpload}
              />
              <label htmlFor="image-upload">
                <Button
                  variant="contained"
                  component="span"
                  startIcon={<AddPhotoIcon />}
                >
                  Upload Images
                </Button>
              </label>
            </Box>

            <Grid container spacing={2}>
              {item.images.map((image) => (
                <Grid item xs={12} sm={6} md={4} key={image.id}>
                  <Box
                    sx={{
                      position: 'relative',
                      borderRadius: 2,
                      overflow: 'hidden',
                      boxShadow: 3,
                      bgcolor: 'background.paper',
                      width: '100%',
                      aspectRatio: '4 / 3',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <img
                      src={`${image.image_path}`}
                      alt="Item"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        borderRadius: '8px',
                        display: 'block',
                        background: '#f5f5f5',
                      }}
                      onError={e => (e.currentTarget.style.background = '#eee')}
                    />
                    <IconButton
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        transition: 'background 0.2s',
                        '&:hover': {
                          backgroundColor: 'rgba(255, 0, 0, 0.2)',
                          color: 'red',
                        },
                      }}
                      onClick={() => handleDeleteImage(image.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
              >
                Save
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/admin/items')}
              >
                Cancel
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          Are you sure you want to delete this image?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteImage} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ItemForm; 