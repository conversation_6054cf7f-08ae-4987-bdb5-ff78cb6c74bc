import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../../api/axios';

interface SubCategory {
  id: number;
  name: string;
  category_id: number;
}

const ManageSubcategories: React.FC = () => {
  const { categoryId } = useParams<{ categoryId: string }>();
  const navigate = useNavigate();
  const [subcategories, setSubcategories] = useState<SubCategory[]>([]);
  const [formData, setFormData] = useState({
    name: '',
  });
  const [editingId, setEditingId] = useState<number | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subcategoryToDelete, setSubcategoryToDelete] = useState<number | null>(null);

  useEffect(() => {
    if (categoryId) {
      fetchSubcategories();
    }
  }, [categoryId]);

  const fetchSubcategories = async () => {
    try {
      const response = await api.get(`/categories/${categoryId}/subcategories/`);
      setSubcategories(response.data);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const subcategoryData = {
        name: formData.name.trim(),
        category_id: Number(categoryId),
      };

      if (!subcategoryData.name) {
        console.error('Subcategory name cannot be empty');
        return;
      }

      if (editingId) {
        await api.put(`/categories/${categoryId}/subcategories/${editingId}/`, subcategoryData);
      } else {
        await api.post(`/categories/${categoryId}/subcategories/`, subcategoryData);
      }
      fetchSubcategories();
      setFormData({ name: '' });
      setEditingId(null);
    } catch (error) {
      console.error('Error saving subcategory:', error);
    }
  };

  const handleEdit = (subcategory: SubCategory) => {
    setFormData({
      name: subcategory.name,
    });
    setEditingId(subcategory.id);
  };

  const handleDelete = (id: number) => {
    setSubcategoryToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (subcategoryToDelete) {
      try {
        await api.delete(`/categories/${categoryId}/subcategories/${subcategoryToDelete}/`);
        fetchSubcategories();
      } catch (error) {
        console.error('Error deleting subcategory:', error);
      }
    }
    setDeleteDialogOpen(false);
    setSubcategoryToDelete(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Manage Subcategories</Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/admin/categories')}
        >
          Back to Categories
        </Button>
      </Box>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Subcategory Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <Button type="submit" variant="contained" color="primary">
              {editingId ? 'Update Subcategory' : 'Add Subcategory'}
            </Button>
            {editingId && (
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => {
                  setFormData({ name: '' });
                  setEditingId(null);
                }}
                sx={{ ml: 2 }}
              >
                Cancel
              </Button>
            )}
          </Grid>
        </Grid>
      </form>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Existing Subcategories
        </Typography>
        <Grid container spacing={2}>
          {subcategories.map((subcategory) => (
            <Grid item xs={12} sm={6} md={4} key={subcategory.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6">{subcategory.name}</Typography>
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                    <IconButton onClick={() => handleEdit(subcategory)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(subcategory.id)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Subcategory</DialogTitle>
        <DialogContent>
          Are you sure you want to delete this subcategory?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ManageSubcategories; 