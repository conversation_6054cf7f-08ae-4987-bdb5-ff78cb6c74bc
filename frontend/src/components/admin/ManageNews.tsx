import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Card,
  CardContent,
  Alert,
  Snackbar,
} from '@mui/material';
import { Save as SaveIcon } from '@mui/icons-material';
import api from '../../api/axios';

interface News {
  id: number;
  title: string;
  content: string;
  created_at: string;
  updated_at: string;
}

const ManageNews: React.FC = () => {
  const [news, setNews] = useState<News | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  useEffect(() => {
    fetchNews();
  }, []);

  const fetchNews = async () => {
    try {
      const response = await api.get('/news/');
      setNews(response.data);
      setFormData({
        title: response.data.title,
        content: response.data.content || '',
      });
      setLoading(false);
    } catch (error) {
      console.error('Error fetching news:', error);
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      if (news && news.id > 0) {
        // Update existing news
        await api.put(`/news/${news.id}`, formData);
        setSnackbar({
          open: true,
          message: 'News updated successfully!',
          severity: 'success',
        });
      } else {
        // Create new news
        await api.post('/news/', formData);
        setSnackbar({
          open: true,
          message: 'News created successfully!',
          severity: 'success',
        });
      }
      
      // Refresh the news data
      await fetchNews();
    } catch (error) {
      console.error('Error saving news:', error);
      setSnackbar({
        open: true,
        message: 'Error saving news. Please try again.',
        severity: 'error',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Manage Homepage News
      </Typography>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Edit News Content
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            This content will be displayed in the "Latest News" card on the homepage.
          </Typography>
          
          <form onSubmit={handleSubmit}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <TextField
                fullWidth
                label="Title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                variant="outlined"
              />
              
              <TextField
                fullWidth
                label="Content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                multiline
                rows={8}
                variant="outlined"
                placeholder="Enter your news content here..."
              />
              
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save News'}
                </Button>
                
                {news && (
                  <Typography variant="body2" color="text.secondary">
                    Last updated: {new Date(news.updated_at).toLocaleString()}
                  </Typography>
                )}
              </Box>
            </Box>
          </form>
        </CardContent>
      </Card>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ManageNews;
