import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.models import Base, User
from app.crud import get_password_hash, verify_password, create_user
from app.schemas import UserCreate

# Database connection using environment variables
DATABASE_URL = f"mysql+pymysql://{os.getenv('MYSQL_USER', 'lsktrading_user')}:{os.getenv('MYSQL_PASSWORD', 'change_this_password_123')}@mysql:3306/{os.getenv('MYSQL_DATABASE', 'lsktrading')}?charset=utf8mb4"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_db_connection():
    try:
        print("Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT DATABASE()"))
            db_name = result.scalar()
            print(f"Connected to database: {db_name}")
            return True
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return False

def init_db():
    try:
        print("\nCreating database tables...")
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully")
        
        # Verify tables were created
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print("\nExisting tables:")
            for table in tables:
                print(f"- {table[0]}")
                
            # Check users table structure
            print("\nUsers table structure:")
            result = conn.execute(text("DESCRIBE users"))
            columns = result.fetchall()
            for col in columns:
                print(f"- {col[0]} ({col[1]})")
    except Exception as e:
        print(f"Error creating tables: {e}")
        sys.exit(1)

def create_admin_user():
    if not test_db_connection():
        print("Failed to connect to database")
        sys.exit(1)
        
    db = SessionLocal()
    try:
        # First create the tables
        init_db()
        
        # Check if admin user already exists
        admin = db.query(User).filter(User.username == "admin").first()
        if admin:
            print("\nAdmin user already exists")
            print(f"Username: {admin.username}")
            print(f"Email: {admin.email}")
            print(f"Is Admin: {admin.is_admin}")
            # Verify the password
            is_valid = verify_password("admin123", admin.password_hash)
            print(f"Password verification: {'Valid' if is_valid else 'Invalid'}")
            return

        # Create admin user
        password = "admin123"
        password_hash = get_password_hash(password)
        print(f"\nCreating admin user with password hash: {password_hash}")
        
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=password_hash,
            is_admin=True
        )
        db.add(admin_user)
        db.commit()
        print("\nAdmin user created successfully")
        print(f"Username: {admin_user.username}")
        print(f"Email: {admin_user.email}")
        print(f"Is Admin: {admin_user.is_admin}")
        
        # Verify the user was created
        created_user = db.query(User).filter(User.username == "admin").first()
        if created_user:
            print("\nVerification:")
            print(f"Found user: {created_user.username}")
            print(f"Password verification: {verify_password('admin123', created_user.password_hash)}")
            print(f"Is Admin: {created_user.is_admin}")
            
            # Also verify using raw SQL
            with engine.connect() as conn:
                result = conn.execute(text("SELECT * FROM users WHERE username = 'admin'"))
                user_data = result.fetchone()
                if user_data:
                    print("\nRaw SQL verification:")
                    print(f"Username: {user_data[1]}")
                    print(f"Email: {user_data[2]}")
                    print(f"Is Admin: {user_data[4]}")
    except Exception as e:
        print(f"Error creating admin user: {e}")
        sys.exit(1)
    finally:
        db.close()

def main():
    db = SessionLocal()
    try:
        # Create Martin admin
        martin = UserCreate(
            username="martin",
            email="<EMAIL>",
            password="Sophie6060!"
        )
        create_user(db, martin)
        print("Created admin user: martin")

        # Create Mattias admin
        mattias = UserCreate(
            username="mattias",
            email="<EMAIL>",
            password="Mossbanken123!"
        )
        create_user(db, mattias)
        print("Created admin user: mattias")

    finally:
        db.close()

if __name__ == "__main__":
    main() 