import React, { useState, useEffect } from 'react';
import { Container, Typo<PERSON>, Box, Grid, Card, CardContent, CardMedia, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { APP_VERSION } from '../version';
import api from '../api/axios';

interface TodaysPickItem {
  id: number;
  name: string;
  description: string;
  price: number;
  article_number: string | null;
  is_used: boolean;
  active: boolean;
  quantity: number;
  category_id: number;
  subcategory_id: number | null;
  brand_id: number;
  category: {
    id: number;
    name: string;
    subcategory_names: string[];
  };
  subcategory: {
    id: number;
    name: string;
    category_id: number;
    created_at: string;
    updated_at: string;
  } | null;
  brand: {
    id: number;
    name: string;
  };
  images: { id: number; image_path: string }[];
}

interface News {
  id: number;
  title: string;
  content: string;
}

const Home: React.FC = () => {
  const [todaysPick, setTodaysPick] = useState<TodaysPickItem[]>([]);
  const [news, setNews] = useState<News | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [pickResponse, newsResponse] = await Promise.all([
          api.get('/news/todays-pick'),
          api.get('/news/')
        ]);

        console.log('Today\'s pick response:', pickResponse.data);
        console.log('News response:', newsResponse.data);

        setTodaysPick(pickResponse.data || []);
        setNews(newsResponse.data);
      } catch (error) {
        console.error('Error fetching homepage data:', error);
        setError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <Container maxWidth="lg" sx={{ position: 'relative', minHeight: '80vh' }}>
      <Box sx={{ my: 4 }}>
        <Typography variant="h2" component="h1" gutterBottom align="center">
          Welcome to LSK Trading
        </Typography>
        <Typography variant="h5" component="h2" gutterBottom align="center" color="text.secondary">
          Your Trusted Maritime Spare Parts Supplier
        </Typography>

        <Box sx={{ my: 4 }}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h5" component="h2" gutterBottom>
                      Browse Our Catalogue
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Explore our extensive collection of maritime spare parts from leading manufacturers.
                    </Typography>
                  </Box>
                  <Button variant="contained" component={RouterLink} to="/items" sx={{ alignSelf: 'flex-start' }}>
                    View Items
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h5" component="h2" gutterBottom>
                      Contact Us
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Have questions about our products? Get in touch with our team.
                    </Typography>
                  </Box>
                  <Button variant="contained" component={RouterLink} to="/contact" sx={{ alignSelf: 'flex-start' }}>
                    Contact Form
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h5" component="h2" gutterBottom>
                    Today's Pick
                  </Typography>
                  {loading ? (
                    <Typography variant="body2" color="text.secondary">
                      Loading today's featured items...
                    </Typography>
                  ) : error ? (
                    <Typography variant="body2" color="error">
                      Unable to load featured items
                    </Typography>
                  ) : todaysPick.length > 0 ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {todaysPick.map((item) => (
                        <Box key={item.id} sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                          {item.images && item.images.length > 0 ? (
                            <Box
                              component="img"
                              src={item.images[0].image_path}
                              alt={item.name}
                              sx={{
                                width: 60,
                                height: 60,
                                objectFit: 'cover',
                                borderRadius: 1,
                                flexShrink: 0
                              }}
                            />
                          ) : (
                            <Box
                              sx={{
                                width: 60,
                                height: 60,
                                bgcolor: 'grey.200',
                                borderRadius: 1,
                                flexShrink: 0,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <Typography variant="caption" color="text.secondary">
                                No Image
                              </Typography>
                            </Box>
                          )}
                          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                            <Typography variant="subtitle2" noWrap>
                              {item.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical'
                            }}>
                              {item.description || 'No description available'}
                            </Typography>
                            <Typography variant="subtitle2" color="primary">
                              {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Contact us'}
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No featured items available
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h5" component="h2" gutterBottom>
                    {news?.title || 'Latest News'}
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {news?.content || 'Loading news...'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Box>

      {/* Version display in lower right corner */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          opacity: 0.6,
          fontSize: '0.75rem',
          color: 'text.secondary'
        }}
      >
        v{APP_VERSION}
      </Box>
    </Container>
  );
};

export default Home;