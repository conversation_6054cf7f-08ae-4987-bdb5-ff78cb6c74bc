from sqlalchemy import Column, Integer, String, ForeignKey, Text, Boolean, Numeric, DateTime, Float
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.sql import text
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    email = Column(String(120), unique=True, index=True, nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)

class Brand(Base):
    __tablename__ = "brands"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    items = relationship("Item", back_populates="brand")

class SubCategory(Base):
    __tablename__ = 'subcategories'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=False)
    created_at = Column(DateTime, nullable=False, server_default=text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime, nullable=False, server_default=text('CURRENT_TIMESTAMP'))

    category = relationship('Category', back_populates='subcategories')
    items = relationship('Item', back_populates='subcategory')

    def __repr__(self):
        return f'<SubCategory {self.name}>'

class Category(Base):
    __tablename__ = 'categories'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    created_at = Column(DateTime, nullable=False, server_default=text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime, nullable=False, server_default=text('CURRENT_TIMESTAMP'))

    items = relationship('Item', back_populates='category')
    subcategories = relationship('SubCategory', back_populates='category', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Category {self.name}>'

class Item(Base):
    __tablename__ = "items"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)  # Allow NULL descriptions for external apps
    article_number = Column(String(100), nullable=True)
    price = Column(Float, nullable=False)
    is_used = Column(Boolean, default=False)
    active = Column(Boolean, default=True)
    quantity = Column(Integer, default=0)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=False)
    subcategory_id = Column(Integer, ForeignKey("subcategories.id"), nullable=True)
    brand_id = Column(Integer, ForeignKey("brands.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    brand = relationship("Brand", back_populates="items")
    category = relationship("Category", back_populates="items")
    subcategory = relationship("SubCategory", back_populates="items")
    images = relationship("ItemImage", back_populates="item", cascade="all, delete-orphan")

class ItemImage(Base):
    __tablename__ = "item_images"
    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, ForeignKey("items.id"))
    image_path = Column(String(255), nullable=False)
    item = relationship("Item", back_populates="images")