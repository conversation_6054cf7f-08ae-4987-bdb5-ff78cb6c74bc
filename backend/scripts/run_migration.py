from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def table_exists(session, table_name):
    try:
        session.execute(text(f"SELECT 1 FROM {table_name} LIMIT 1"))
        return True
    except Exception:
        return False

def column_exists(session, table_name, column_name):
    try:
        session.execute(text(f"SELECT {column_name} FROM {table_name} LIMIT 1"))
        return True
    except Exception:
        return False

def run_migration():
    print("Starting database migrations...")

    # Create a session
    session = SessionLocal()

    try:
        # Create users table if it doesn't exist
        if not table_exists(session, 'users'):
            print("Creating users table...")
            session.execute(text("""
                CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(128) NOT NULL,
                    email VARCHAR(120) UNIQUE NOT NULL,
                    is_admin BOOLEAN DEFAULT FALSE NOT NULL
                )
            """))
        else:
            print("Users table already exists")

        # Create brands table if it doesn't exist
        if not table_exists(session, 'brands'):
            print("Creating brands table...")
            session.execute(text("""
                CREATE TABLE brands (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) UNIQUE NOT NULL
                )
            """))
        else:
            print("Brands table already exists")

        # Create categories table if it doesn't exist
        if not table_exists(session, 'categories'):
            print("Creating categories table...")
            session.execute(text("""
                CREATE TABLE categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """))
        else:
            print("Categories table already exists")
            # Add missing columns to categories table
            if not column_exists(session, 'categories', 'created_at'):
                print("Adding created_at column to categories table...")
                session.execute(text("""
                    ALTER TABLE categories
                    ADD COLUMN created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                """))

            if not column_exists(session, 'categories', 'updated_at'):
                print("Adding updated_at column to categories table...")
                session.execute(text("""
                    ALTER TABLE categories
                    ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                """))

        # Create subcategories table if it doesn't exist
        if not table_exists(session, 'subcategories'):
            print("Creating subcategories table...")
            session.execute(text("""
                CREATE TABLE subcategories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    category_id INT NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id)
                )
            """))
        else:
            print("Subcategories table already exists")

        # Create items table if it doesn't exist
        if not table_exists(session, 'items'):
            print("Creating items table...")
            session.execute(text("""
                CREATE TABLE items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT NOT NULL,
                    price FLOAT NOT NULL,
                    article_number VARCHAR(100),
                    is_used BOOLEAN DEFAULT FALSE,
                    active BOOLEAN DEFAULT TRUE,
                    category_id INT NOT NULL,
                    subcategory_id INT,
                    brand_id INT,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id),
                    FOREIGN KEY (subcategory_id) REFERENCES subcategories(id),
                    FOREIGN KEY (brand_id) REFERENCES brands(id)
                )
            """))
        else:
            print("Items table already exists")
            # Add missing columns to items table
            if not column_exists(session, 'items', 'is_used'):
                print("Adding is_used column to items table...")
                session.execute(text("""
                    ALTER TABLE items
                    ADD COLUMN is_used BOOLEAN DEFAULT FALSE
                """))

            if not column_exists(session, 'items', 'active'):
                print("Adding active column to items table...")
                session.execute(text("""
                    ALTER TABLE items
                    ADD COLUMN active BOOLEAN DEFAULT TRUE
                """))

            if not column_exists(session, 'items', 'subcategory_id'):
                print("Adding subcategory_id column to items table...")
                session.execute(text("""
                    ALTER TABLE items
                    ADD COLUMN subcategory_id INT,
                    ADD FOREIGN KEY (subcategory_id) REFERENCES subcategories(id)
                """))

            if not column_exists(session, 'items', 'article_number'):
                print("Adding article_number column to items table...")
                session.execute(text("""
                    ALTER TABLE items
                    ADD COLUMN article_number VARCHAR(100)
                """))

            if not column_exists(session, 'items', 'created_at'):
                print("Adding created_at column to items table...")
                session.execute(text("""
                    ALTER TABLE items
                    ADD COLUMN created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                """))

            if not column_exists(session, 'items', 'updated_at'):
                print("Adding updated_at column to items table...")
                session.execute(text("""
                    ALTER TABLE items
                    ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                """))

            if not column_exists(session, 'items', 'quantity'):
                print("Adding quantity column to items table...")
                session.execute(text("""
                    ALTER TABLE items
                    ADD COLUMN quantity INT DEFAULT 0
                """))

            # Check if description column is nullable (for external app compatibility)
            try:
                result = session.execute(text("""
                    SELECT IS_NULLABLE
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'items'
                    AND COLUMN_NAME = 'description'
                """))
                is_nullable = result.scalar()

                if is_nullable == 'NO':
                    print("Making description column nullable for external app compatibility...")
                    session.execute(text("""
                        ALTER TABLE items
                        MODIFY COLUMN description TEXT NULL
                    """))
                else:
                    print("Description column is already nullable")
            except Exception as e:
                print(f"Could not check/modify description column: {e}")

            # Create news table for homepage content
            if not table_exists(session, 'news'):
                print("Creating news table...")
                session.execute(text("""
                    CREATE TABLE news (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL DEFAULT 'Latest News',
                        content TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                """))

                # Insert default news content
                session.execute(text("""
                    INSERT INTO news (title, content) VALUES
                    ('Latest News', 'Welcome to LSK Trading! We are your trusted maritime spare parts supplier. Check back here for updates, new arrivals, and company announcements.')
                """))
                print("News table created with default content")
            else:
                print("News table already exists")

        # Create item_images table if it doesn't exist
        if not table_exists(session, 'item_images'):
            print("Creating item_images table...")
            session.execute(text("""
                CREATE TABLE item_images (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    item_id INT NOT NULL,
                    image_path VARCHAR(255) NOT NULL,
                    FOREIGN KEY (item_id) REFERENCES items(id)
                )
            """))
        else:
            print("Item_images table already exists")

        # Commit all changes
        session.commit()
        print("Database migrations completed successfully!")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
        session.rollback()
        raise
    finally:
        session.close()

if __name__ == "__main__":
    run_migration()