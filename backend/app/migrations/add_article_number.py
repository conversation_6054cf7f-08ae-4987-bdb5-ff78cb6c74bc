"""add article number

Revision ID: add_article_number
Revises: 
Create Date: 2024-05-02 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_article_number'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Add article_number column to items table
    op.add_column('items', sa.Column('article_number', sa.String(50), nullable=True))

def downgrade():
    # Remove article_number column from items table
    op.drop_column('items', 'article_number') 