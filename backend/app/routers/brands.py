from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from ..deps import get_db
from ..crud import create_brand, get_brands, get_brand, update_brand, delete_brand
from ..schemas import BrandCreate, BrandOut

router = APIRouter()

@router.get("", response_model=List[BrandOut])  # ← no slash here
@router.get("/", response_model=List[BrandOut])
def read_brands(db: Session = Depends(get_db)):
    return get_brands(db)

@router.post("", response_model=BrandOut)  # ← no slash here
@router.post("/", response_model=BrandOut)  # ← no slash here
def create_new_brand(brand: BrandCreate, db: Session = Depends(get_db)):
    return create_brand(db=db, brand=brand)

@router.put("/{brand_id}", response_model=BrandOut)
@router.put("/{brand_id}/", response_model=BrandOut)
def update_existing_brand(brand_id: int, brand: BrandCreate, db: Session = Depends(get_db)):
    db_brand = get_brand(db, brand_id)
    if db_brand is None:
        raise HTTPException(status_code=404, detail="Brand not found")
    return update_brand(db=db, brand_id=brand_id, brand=brand)

@router.delete("/{brand_id}")
@router.delete("/{brand_id}/")
def delete_existing_brand(brand_id: int, db: Session = Depends(get_db)):
    db_brand = get_brand(db, brand_id)
    if db_brand is None:
        raise HTTPException(status_code=404, detail="Brand not found")
    delete_brand(db=db, brand_id=brand_id)
    return {"message": "Brand deleted successfully"}
