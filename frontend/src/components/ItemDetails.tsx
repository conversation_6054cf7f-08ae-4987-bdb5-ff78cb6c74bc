import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Grid, Paper, Rating } from '@mui/material';
import { useParams } from 'react-router-dom';
import api, { imageApi } from '../api/axios';

interface Item {
  id: number;
  name: string;
  description: string;
  price: number;
  images: Array<{
    id: number;
    image_path: string;
  }>;
  brand: {
    id: number;
    name: string;
  };
  category: {
    id: number;
    name: string;
  };
}

const ItemDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [item, setItem] = useState<Item | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchItem = async () => {
      try {
        const response = await api.get(`/items/${id}?active_only=true`);
        setItem(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load item details');
        setLoading(false);
      }
    };

    fetchItem();
  }, [id]);

  if (loading) return <Typography>Loading...</Typography>;
  if (error) return <Typography color="error">{error}</Typography>;
  if (!item) return <Typography>Item not found</Typography>;

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper elevation={3} sx={{ p: 2 }}>
            {item.images && item.images.length > 0 ? (
              <img
                src={`${item.images[0].image_path}`}
                alt={item.name}
                style={{ width: '100%', height: '300px', objectFit: 'cover' }}
              />
            ) : (
              <Box
                sx={{
                  width: '100%',
                  height: '300px',
                  bgcolor: 'grey.200',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography>No image available</Typography>
              </Box>
            )}
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={3} sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
              {item.name}
            </Typography>
            <Typography variant="h6" color="primary" gutterBottom>
              {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Contact us for price'}
            </Typography>
            <Typography variant="body1" paragraph>
              {item.description}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Brand: {item.brand.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Category: {item.category.name}
            </Typography>
            <Button variant="contained" color="primary" sx={{ mt: 2 }}>
              Add to Cart
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ItemDetails;