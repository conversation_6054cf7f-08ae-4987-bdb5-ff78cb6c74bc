from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from .. import crud, models, schemas
from ..database import get_db

router = APIRouter()

@router.get("/", response_model=schemas.NewsOut)
def get_news(db: Session = Depends(get_db)):
    """Get the latest news content"""
    news = crud.get_news(db)
    if not news:
        # Return default news if none exists
        from datetime import datetime
        return schemas.NewsOut(
            id=0,
            title="Latest News",
            content="Welcome to LSK Trading! Check back for updates and announcements.",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    return schemas.NewsOut.from_orm(news)

@router.post("/", response_model=schemas.NewsOut)
def create_news(news: schemas.NewsCreate, db: Session = Depends(get_db)):
    """Create news content (admin only)"""
    db_news = crud.create_news(db=db, news=news)
    return schemas.NewsOut.from_orm(db_news)

@router.put("/{news_id}", response_model=schemas.NewsOut)
def update_news(news_id: int, news_update: schemas.NewsUpdate, db: Session = Depends(get_db)):
    """Update news content (admin only)"""
    db_news = crud.update_news(db, news_id=news_id, news_update=news_update)
    if db_news is None:
        raise HTTPException(status_code=404, detail="News not found")
    return schemas.NewsOut.from_orm(db_news)

@router.get("/todays-pick", response_model=List[schemas.ItemOut])
def get_todays_pick(db: Session = Depends(get_db)):
    """Get 2 random active items for Today's Pick"""
    items = crud.get_random_active_items(db, limit=2)
    return [schemas.ItemOut.from_orm(item) for item in items]
