from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from ..deps import get_db
from ..crud import (
    create_category, get_categories, update_category, delete_category, get_category,
    get_subcategories_by_category, create_subcategory, update_subcategory, delete_subcategory
)
from ..schemas import CategoryCreate, CategoryOut, SubCategoryOut, SubCategoryCreate

router = APIRouter()

@router.get("", response_model=List[CategoryOut])  # no trailing slash
@router.get("/", response_model=List[CategoryOut])
def read_categories(db: Session = Depends(get_db)):
    return get_categories(db)

@router.post("", response_model=CategoryOut)  # no trailing slash
@router.post("/", response_model=CategoryOut)  # no trailing slash
def create_new_category(category: CategoryCreate, db: Session = Depends(get_db)):
    return create_category(db=db, category=category)

@router.put("/{category_id}", response_model=CategoryOut)
@router.put("/{category_id}/", response_model=CategoryOut)
def update_existing_category(category_id: int, category: CategoryCreate, db: Session = Depends(get_db)):
    db_category = get_category(db, category_id)
    if db_category is None:
        raise HTTPException(status_code=404, detail="Category not found")
    return update_category(db=db, category_id=category_id, category=category)

@router.delete("/{category_id}")
@router.delete("/{category_id}/")
def delete_existing_category(category_id: int, db: Session = Depends(get_db)):
    db_category = get_category(db, category_id)
    if db_category is None:
        raise HTTPException(status_code=404, detail="Category not found")
    delete_category(db=db, category_id=category_id)
    return {"message": "Category deleted successfully"}

@router.get("/{category_id}/subcategories", response_model=List[SubCategoryOut])
@router.get("/{category_id}/subcategories/", response_model=List[SubCategoryOut])
def read_subcategories(category_id: int, db: Session = Depends(get_db)):
    subcategories = get_subcategories_by_category(db, category_id)
    return subcategories or []

@router.post("/{category_id}/subcategories", response_model=SubCategoryOut)
@router.post("/{category_id}/subcategories/", response_model=SubCategoryOut)
def create_new_subcategory(category_id: int, subcategory: SubCategoryCreate, db: Session = Depends(get_db)):
    if subcategory.category_id != category_id:
        raise HTTPException(status_code=400, detail="Category ID mismatch")
    return create_subcategory(db=db, subcategory=subcategory)

@router.put("/{category_id}/subcategories/{subcategory_id}", response_model=SubCategoryOut)
@router.put("/{category_id}/subcategories/{subcategory_id}/", response_model=SubCategoryOut)
def update_existing_subcategory(
    category_id: int,
    subcategory_id: int,
    subcategory: SubCategoryCreate,
    db: Session = Depends(get_db)
):
    if subcategory.category_id != category_id:
        raise HTTPException(status_code=400, detail="Category ID mismatch")
    updated = update_subcategory(db=db, subcategory_id=subcategory_id, subcategory=subcategory)
    if updated is None:
        raise HTTPException(status_code=404, detail="Subcategory not found")
    return updated

@router.delete("/{category_id}/subcategories/{subcategory_id}")
@router.delete("/{category_id}/subcategories/{subcategory_id}/")
def delete_existing_subcategory(category_id: int, subcategory_id: int, db: Session = Depends(get_db)):
    success = delete_subcategory(db=db, subcategory_id=subcategory_id)
    if not success:
        raise HTTPException(status_code=404, detail="Subcategory not found")
    return {"message": "Subcategory deleted successfully"}
