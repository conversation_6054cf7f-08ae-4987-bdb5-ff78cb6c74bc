version: '3.8'

services:
  db:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_DATABASE: lsktrading
      MYSQL_USER: lskuser
      MYSQL_PASSWORD: lskpassword
      MYSQL_ROOT_PASSWORD: rootpassword
    ports:
      - '3306:3306'
    volumes:
      - db_data:/var/lib/mysql
      - ./backend/scripts:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "lskuser", "-p$$MYSQL_PASSWORD"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  db-migration:
    build: ./backend
    volumes:
      - ./backend:/app
    environment:
      - DATABASE_URL=mysql://lskuser:lskpassword@db:3306/lsktrading?charset=utf8mb4
    command: python scripts/run_migration.py
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app-network

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
    environment:
      - DATABASE_URL=mysql://lskuser:lskpassword@db:3306/lsktrading?charset=utf8mb4
      - GMAIL_USER=<EMAIL>
      - GMAIL_APP_PASSWORD=srkxtghhpjdvqnoe
      - CONTACT_EMAIL=<EMAIL>
      - FRONTEND_URL=http://localhost:3000
    depends_on:
      db-migration:
        condition: service_completed_successfully
    networks:
      - app-network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
      - ./backend/uploads:/app/public/uploads
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    env_file:
      - ./frontend/.env.development
    stdin_open: true
    tty: true
    command: ["npm", "start"]
    depends_on:
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  db_data:
  uploads: 
  frontend_node_modules: