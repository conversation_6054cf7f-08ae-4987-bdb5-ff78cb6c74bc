version: '3.8'

services:
  frontend:
    image: docker.lexbit.se:5000/lsktrading/frontend:latest

    ports:
      - "8040:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - lsktrading-network
   

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    image: docker.lexbit.se:5000/lsktrading/backend:latest    
    environment:
      - DATABASE_URL=mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@mysql:3306/${MYSQL_DATABASE}
      - SECRET_KEY=${SECRET_KEY}
      - GMAIL_USER=<EMAIL>
      - GMAIL_APP_PASSWORD=srkxtghhpjdvqnoe
      - CONTACT_EMAIL=martin.we<PERSON><PERSON>@gmail.com
      - FRONTEND_URL=http://**************:8040
      - PYTHONPATH=/app
    ports:
      - "8041:8000"
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - lsktrading-network
    volumes:
      - item_uploads:/app/uploads  
  
   

  mysql:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_INITDB_SKIP_TZINFO=1
    command: --default-authentication-plugin=mysql_native_password
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    networks:
      - lsktrading-network
  

volumes:
  mysql_data:
    name: lsktrading_mysql_data
  item_uploads:
    name: lsktrading_item_uploads

networks:
  lsktrading-network:
    driver: bridge

