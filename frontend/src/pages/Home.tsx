import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Grid, Card, CardContent, CardMedia, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { APP_VERSION } from '../version';
import api from '../api/axios';

interface TodaysPickItem {
  id: number;
  name: string;
  description: string;
  price: number;
  images: { id: number; image_path: string }[];
}

interface News {
  id: number;
  title: string;
  content: string;
}

const Home: React.FC = () => {
  const [todaysPick, setTodaysPick] = useState<TodaysPickItem[]>([]);
  const [news, setNews] = useState<News | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [pickResponse, newsResponse] = await Promise.all([
          api.get('/news/todays-pick'),
          api.get('/news/')
        ]);
        setTodaysPick(pickResponse.data);
        setNews(newsResponse.data);
      } catch (error) {
        console.error('Error fetching homepage data:', error);
      }
    };

    fetchData();
  }, []);

  return (
    <Container maxWidth="lg" sx={{ position: 'relative', minHeight: '80vh' }}>
      <Box sx={{ my: 4 }}>
        <Typography variant="h2" component="h1" gutterBottom align="center">
          Welcome to LSK Trading
        </Typography>
        <Typography variant="h5" component="h2" gutterBottom align="center" color="text.secondary">
          Your Trusted Maritime Spare Parts Supplier
        </Typography>

        <Box sx={{ my: 4 }}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h5" component="h2" gutterBottom>
                      Browse Our Catalogue
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Explore our extensive collection of maritime spare parts from leading manufacturers.
                    </Typography>
                  </Box>
                  <Button variant="contained" component={RouterLink} to="/items" sx={{ alignSelf: 'flex-start' }}>
                    View Items
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h5" component="h2" gutterBottom>
                      Contact Us
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Have questions about our products? Get in touch with our team.
                    </Typography>
                  </Box>
                  <Button variant="contained" component={RouterLink} to="/contact" sx={{ alignSelf: 'flex-start' }}>
                    Contact Form
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h5" component="h2" gutterBottom>
                    Today's Pick
                  </Typography>
                  {todaysPick.length > 0 ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {todaysPick.map((item) => (
                        <Box key={item.id} sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                          <Box
                            component="img"
                            src={item.images[0]?.image_path || '/placeholder.jpg'}
                            alt={item.name}
                            sx={{
                              width: 60,
                              height: 60,
                              objectFit: 'cover',
                              borderRadius: 1,
                              flexShrink: 0
                            }}
                          />
                          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                            <Typography variant="subtitle2" noWrap>
                              {item.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical'
                            }}>
                              {item.description}
                            </Typography>
                            <Typography variant="subtitle2" color="primary">
                              {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Contact us'}
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Loading today's featured items...
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h5" component="h2" gutterBottom>
                    {news?.title || 'Latest News'}
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {news?.content || 'Loading news...'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Box>

      {/* Version display in lower right corner */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          opacity: 0.6,
          fontSize: '0.75rem',
          color: 'text.secondary'
        }}
      >
        v{APP_VERSION}
      </Box>
    </Container>
  );
};

export default Home;