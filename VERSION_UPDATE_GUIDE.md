# Version Update Guide

## Current Version: 1.1.0

This guide explains how to update version numbers for both frontend and backend when making releases.

## Backend Version

**File to update:** `backend/app/main.py`

```python
# Version constant - increment this with each release
API_VERSION = "1.1.0"  # <-- Update this line
```

**Check backend version:**
- Local: `curl http://localhost:8000/api/version`
- Production: `curl https://www.lsktrading.com/api/version`
- Alternative: `curl https://www.lsktrading.com/` (root endpoint also shows version)

## Frontend Version

**File to update:** `frontend/src/version.ts`

```typescript
// Version constant - increment this with each release
export const APP_VERSION = "1.1.0";  // <-- Update this line
```

**Check frontend version:**
- Visit the homepage and look in the lower right corner
- Local: http://localhost:3000
- Production: https://www.lsktrading.com

## Version Update Process

1. **Before making changes:** Note the current version
2. **After making changes:** Increment the version in both files
3. **Deploy:** Run the Concourse pipeline
4. **Verify:** Check both endpoints/displays show the new version

## Version Numbering

Use semantic versioning: `MAJOR.MINOR.PATCH`

- **MAJOR:** Breaking changes
- **MINOR:** New features, backward compatible
- **PATCH:** Bug fixes, backward compatible

Examples:
- Bug fix: 1.1.0 → 1.1.1
- New feature: 1.1.0 → 1.2.0
- Breaking change: 1.1.0 → 2.0.0
