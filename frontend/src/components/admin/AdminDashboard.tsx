import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
} from '@mui/material';
import {
  Category as CategoryIcon,
  Inventory as InventoryIcon,
  Logout as LogoutIcon,
  Business as BusinessIcon,
  Article as NewsIcon,
} from '@mui/icons-material';

const AdminDashboard: React.FC = () => {
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    navigate('/admin/login');
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Admin Dashboard
          </Typography>
          <Button
            variant="outlined"
            startIcon={<LogoutIcon />}
            onClick={handleLogout}
          >
            Logout
          </Button>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <Paper
              sx={{
                p: 3,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: 6,
                },
              }}
              onClick={() => navigate('/admin/items')}
            >
              <InventoryIcon sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" component="h2">
                Manage Items
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Add, edit, and delete items in the catalog
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Paper
              sx={{
                p: 3,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: 6,
                },
              }}
              onClick={() => navigate('/admin/categories')}
            >
              <CategoryIcon sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" component="h2">
                Manage Categories
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Add, edit, and delete product categories
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Paper
              sx={{
                p: 3,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: 6,
                },
              }}
              onClick={() => navigate('/admin/brands')}
            >
              <BusinessIcon sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" component="h2">
                Manage Brands
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Add, edit, and delete product brands
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Paper
              sx={{
                p: 3,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: 6,
                },
              }}
              onClick={() => navigate('/admin/news')}
            >
              <NewsIcon sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" component="h2">
                Manage News
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Edit homepage news content
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default AdminDashboard;