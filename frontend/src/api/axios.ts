import axios from 'axios';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Don't allow redirects that might change the domain/port
  maxRedirects: 0,
  // Add withCredentials for CORS if needed
  withCredentials: false,
});

// Add request interceptor for better error handling - don't add /api again
api.interceptors.request.use(
  (config) => {
    // Don't modify URLs - they already have /api from baseURL
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for better error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Log errors for debugging
    console.error('API Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request made but no response received');
      console.error('Request:', error.request);
    }
    return Promise.reject(error);
  }
);

// Create a separate instance for image requests
export const imageApi = axios.create({
  baseURL: process.env.REACT_APP_IMAGE_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export default api; 