import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi import Depends
from sqlalchemy.orm import Session

# Get database URL from environment or use default
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "mysql+pymysql://lskuser:lskpassword@db:3306/lsktrading?charset=utf8mb4"
)

# Create engine with connection pooling and other optimizations
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=5,
    max_overflow=10
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 