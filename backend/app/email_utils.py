import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from typing import Optional
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from .models import Item
from .database import get_db

def send_contact_email(name: str, email: str, message: str, item_id: Optional[str] = None, db: Session = None):
    # Gmail SMTP settings
    smtp_host = "smtp.gmail.com"
    smtp_port = 587
    smtp_user = os.getenv("GMAIL_USER")
    smtp_password = os.getenv("GMAIL_APP_PASSWORD")
    receiver = os.getenv("CONTACT_EMAIL")

    msg = MIMEMultipart()
    msg["From"] = smtp_user
    msg["To"] = receiver
    msg["Subject"] = "New Contact Request from LSK Trading Catalogue"

    # Create the email body with HTML formatting
    body = f"""
    <html>
        <body>
            <h2>New Contact Request</h2>
            <p><strong>Name:</strong> {name}</p>
            <p><strong>Email:</strong> {email}</p>
            <p><strong>Message:</strong></p>
            <p>{message}</p>
    """
    
    if item_id and db:
        try:
            item = db.query(Item).filter(Item.id == item_id).first()
            if item:
                item_url = f"{os.getenv('FRONTEND_URL', 'http://localhost:3000')}/items/{item_id}"
                main_image = f"{os.getenv('FRONTEND_URL', 'http://localhost:3000')}{item.images[0].image_path}" if item.images else None
                
                body += f"""
                    <div style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                        <h3>Item Details</h3>
                        {f'<img src="{main_image}" alt="{item.name}" style="max-width: 300px; margin-bottom: 10px;">' if main_image else ''}
                        <p><strong>Name:</strong> {item.name}</p>
                        {f'<p><strong>Article Number:</strong> {item.article_number}</p>' if item.article_number else ''}
                        <p><strong>Brand:</strong> {item.brand.name}</p>
                        <p><strong>Category:</strong> {item.category.name}</p>
                        <p><strong>Description:</strong> {item.description}</p>
                        <p><a href="{item_url}">View Item on Website</a></p>
                    </div>
                """
        except Exception as e:
            print(f"Error fetching item details: {e}")
    
    body += """
        </body>
    </html>
    """

    msg.attach(MIMEText(body, "html"))

    try:
        with smtplib.SMTP(smtp_host, smtp_port) as server:
            server.starttls()
            server.login(smtp_user, smtp_password)
            server.sendmail(smtp_user, receiver, msg.as_string()) 
            return True
    except Exception as e:
        print(f"Error sending email: {e}")
        return False 