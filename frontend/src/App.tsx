import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline, Box } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { AuthProvider } from './context/AuthContext';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import Items from './pages/Items';
import ItemDetails from './pages/ItemDetails';
import Contact from './pages/Contact';
import AdminLogin from './components/admin/AdminLogin';
import AdminDashboard from './components/admin/AdminDashboard';
import ManageCategories from './components/admin/ManageCategories';
import ManageBrands from './components/admin/ManageBrands';
import ItemList from './components/admin/ItemList';
import ItemForm from './components/admin/ItemForm';
import PrivateRoute from './components/PrivateRoute';
import { HelmetProvider } from 'react-helmet-async';
import ManageSubcategories from './components/admin/ManageSubcategories';
import Categories from './components/admin/Categories';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  return (
    <HelmetProvider>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
            <Router>
              <Navbar />
              <Box component="main" sx={{ flexGrow: 1 }}>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/items" element={<Items />} />
                  <Route path="/items/:id" element={<ItemDetails />} />
                  <Route path="/contact/:id?" element={<Contact />} />
                  <Route path="/admin/login" element={<AdminLogin />} />
                  <Route
                    path="/admin/dashboard"
                    element={
                      <PrivateRoute>
                        <AdminDashboard />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/admin/categories"
                    element={
                      <PrivateRoute>
                        <Categories />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/admin/brands"
                    element={
                      <PrivateRoute>
                        <ManageBrands />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/admin/items"
                    element={
                      <PrivateRoute>
                        <ItemList />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/admin/items/new"
                    element={
                      <PrivateRoute>
                        <ItemForm />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/admin/items/:id"
                    element={
                      <PrivateRoute>
                        <ItemForm />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/admin/categories/:categoryId/subcategories"
                    element={
                      <PrivateRoute>
                        <ManageSubcategories />
                      </PrivateRoute>
                    }
                  />
                  <Route path="/" element={<Navigate to="/admin/login" replace />} />
                </Routes>
              </Box>
              <Footer />
            </Router>
          </Box>
        </AuthProvider>
      </ThemeProvider>
    </HelmetProvider>
  );
}

export default App; 