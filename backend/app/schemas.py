from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

class UserBase(BaseModel):
    username: str
    email: EmailStr

class UserCreate(UserBase):
    password: str

class UserOut(UserBase):
    id: int
    class Config:
        orm_mode = True

class BrandBase(BaseModel):
    name: str

class BrandCreate(BrandBase):
    pass

class BrandOut(BrandBase):
    id: int

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj is None:
            return None
        data = {
            "id": obj.id,
            "name": obj.name
        }
        return cls(**data)

class SubCategoryBase(BaseModel):
    name: str
    category_id: int

class SubCategoryCreate(SubCategoryBase):
    pass

class SubCategoryOut(SubCategoryBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj is None:
            return None
        data = {
            "id": obj.id,
            "name": obj.name,
            "category_id": obj.category_id,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at
        }
        return cls(**data)

class CategoryBase(BaseModel):
    name: str

class CategoryCreate(CategoryBase):
    pass

class CategoryOut(CategoryBase):
    id: int
    subcategory_names: List[str] = []

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj is None:
            return None
        data = {
            "id": obj.id,
            "name": obj.name,
            "subcategory_names": [sub.name for sub in obj.subcategories] if hasattr(obj, 'subcategories') else []
        }
        return cls(**data)

class ItemImageOut(BaseModel):
    id: int
    image_path: str

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj is None:
            return None
        data = {
            "id": obj.id,
            "image_path": obj.image_path
        }
        return cls(**data)

class ItemBase(BaseModel):
    name: str
    description: Optional[str] = None  # Allow None/empty descriptions for external apps
    article_number: Optional[str] = None
    price: float
    is_used: bool
    active: bool
    quantity: int = 0
    category_id: int
    subcategory_id: Optional[int] = None
    brand_id: Optional[int] = None

class ItemCreate(ItemBase):
    pass

class Item(ItemBase):
    id: int
    created_at: datetime
    updated_at: datetime
    category: CategoryOut
    subcategory: Optional[SubCategoryOut] = None
    brand: Optional[BrandOut] = None
    images: List[ItemImageOut] = []

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj is None:
            return None
        data = {
            "id": obj.id,
            "name": obj.name,
            "description": obj.description or "",  # Convert None to empty string for display
            "price": obj.price,
            "article_number": obj.article_number,
            "is_used": obj.is_used,
            "active": obj.active,
            "quantity": obj.quantity,
            "category_id": obj.category_id,
            "subcategory_id": obj.subcategory_id,
            "brand_id": obj.brand_id,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at,
            "category": CategoryOut.from_orm(obj.category) if obj.category else None,
            "subcategory": SubCategoryOut.from_orm(obj.subcategory) if obj.subcategory else None,
            "brand": BrandOut.from_orm(obj.brand) if obj.brand else None,
            "images": [ItemImageOut.from_orm(img) for img in obj.images] if obj.images else []
        }
        return cls(**data)

class ItemOut(ItemBase):
    id: int
    category: Optional[CategoryOut] = None
    subcategory: Optional[SubCategoryOut] = None
    brand: Optional[BrandOut] = None
    images: List[ItemImageOut] = []

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj is None:
            return None
        data = {
            "id": obj.id,
            "name": obj.name,
            "description": obj.description or "",  # Convert None to empty string for display
            "price": obj.price,
            "article_number": obj.article_number,
            "is_used": obj.is_used,
            "active": obj.active,
            "quantity": obj.quantity,
            "category_id": obj.category_id,
            "subcategory_id": obj.subcategory_id,
            "brand_id": obj.brand_id,
            "category": CategoryOut.from_orm(obj.category) if obj.category else None,
            "subcategory": SubCategoryOut.from_orm(obj.subcategory) if obj.subcategory else None,
            "brand": BrandOut.from_orm(obj.brand) if obj.brand else None,
            "images": [ItemImageOut.from_orm(img) for img in obj.images] if obj.images else []
        }
        return cls(**data)

class ItemUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    article_number: Optional[str] = None
    brand_id: Optional[int] = None
    category_id: Optional[int] = None
    subcategory_id: Optional[int] = None
    quantity: Optional[int] = None
    is_used: Optional[bool] = None
    active: Optional[bool] = None
    class Config:
        orm_mode = True

# News schemas
class NewsBase(BaseModel):
    title: str
    content: Optional[str] = None

class NewsCreate(NewsBase):
    pass

class NewsUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None

class NewsOut(NewsBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj is None:
            return None
        return cls(
            id=obj.id,
            title=obj.title,
            content=obj.content or "",
            created_at=obj.created_at,
            updated_at=obj.updated_at
        )