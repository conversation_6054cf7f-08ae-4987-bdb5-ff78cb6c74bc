from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, EmailStr
from typing import Optional
from sqlalchemy.orm import Session
from ..email_utils import send_contact_email
from ..database import get_db

router = APIRouter()

class ContactForm(BaseModel):
    name: str
    email: EmailStr
    message: str
    item_id: Optional[str] = None

@router.post("")
@router.post("/")
async def submit_contact_form(form: ContactForm, db: Session = Depends(get_db)):
    try:
        success = send_contact_email(
            name=form.name,
            email=form.email,
            message=form.message,
            item_id=form.item_id,
            db=db
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to send email")
            
        return {"message": "Contact form submitted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 